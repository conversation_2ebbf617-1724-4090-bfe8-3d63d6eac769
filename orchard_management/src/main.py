import asyncio
import numpy as np
from worker import Worker, Staff

def main(workers: list[str]):
    # Initialize staff
    Staff()

    try:
        for worker in workers:
            Worker(name = worker)
    except Exception as e: 
        print(f'Seems you fucked up: {e}')
    

if __name__ == main:
    workers = ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]
    workers = workers.np.array(workers)
    main(workers)
