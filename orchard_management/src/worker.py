from dataclasses import dataclass
from typing import Literal
import uuid


@dataclass
class Worker:
    name: str = ""
    id: int = 0
    current_daily_total_picks: int = 0
    total_picks: int = 0
    days_worked: int = 0
    average_daily_picks: float = 0.0

    def __post_init__(self):
        # generate a uuid for the worker
        self.id = uuid.uuid4().int

    def update_average(self):
        self.average_daily_picks = (
            self.total_picks / self.days_worked if self.days_worked > 0 else 0.0
        )


@dataclass
class Staff:
    workers: list[Worker] = []
    staff_count: int = len(workers)

    def __post_init__(self):
        if self.workers is None:
            self.workers = []

    def onboard_worker(self, worker: Worker):
        # onbaord worker to staff
        self.staff_count += 1
        self.workers.append(worker)


@dataclass
class Plot:
    map_plot: dict = {}


@dataclass
class Tree:
    id: int = 0
    type: str = Literal[
        "Fuji",
        "Honeycrisp",
        "Granny Smith",
        "Red Delicious",
        "Golden Delicious",
        "Pink Lady",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON> Smith",
        "Red Delicious",
        "Golden Delicious",
        "Pink Lady",
        "<PERSON><PERSON><PERSON>",
        "Cameo",
    ]
    location: dict = {}


@dataclass
class Inventory:
    current_total_apples: int = 0
