import numpy as np


ages = np.array([[14,15,76,29,25],[31,45,16,24,33]])

teenagers = ages < 18

print(np.sum(teenagers))

old_folks = ages[(ages >= 18) & (ages < 65)]

seniors = ages[ages >= 65]
print(seniors)

# you can also use OR operators like |
print(old_folks)

even_ages = ages[ages % 2 == 0]
print(even_ages)


# need to preserve shape of array? you can use the np.where() functions
# comparison operator, array, fill value for those that don't meet condition to maintain original shape
# it's quite a lot slower than using the boolean indexing which flattens! and is faster

adults = np.where(ages >= 18, ages, 0)
print(adults)