import numpy as np

# generate random numbers

rng = np.random.default_rng(seed=1)

#keywords are optional, this gives 1-6 high is exclusive so looks like it's one above
# 3 rows and 2 colums as size
print(rng.integers(low=1,high=7, size=(3,2)))

# seed is a copy like in minecraft



# floating points obviously decimals between 0 and 1
print(np.random.uniform())

# between -1, 1 and 3 rows 2 columsn
print(np.random.uniform(low=-1, high=1, size=(3,2)))



array = np.array([1,2,3,4])

rng.shuffle(array)
random_choice = rng.choice(array)
print(random_choice)


random = rng.choice(array, size=3)
print(random)