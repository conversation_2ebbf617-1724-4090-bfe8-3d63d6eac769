import numpy as np

# <PERSON><PERSON>r arithmetic

array = np.array([1,2,3])


print(array + 1)
print(array / 2)
print(array **5)


# Vectorize math funcs

print(np.sqrt(array))
print(np.round(array))
print(np.ceil(array))
print(np.floor(array))

# radius

radii = np.floor(np.array([1,2,3]))

print(np.floor(np.pi * radii ** 2))



array1 = np.array([1,2,3])
array2 = np.array([4,5,6])


print(array1 + array2)


print(array1 / array2)


# comparison operators

scores = np.array([91,55,100,73,82,64])

print(scores >= 60)


# assign comparison
result = scores[scores < 60] = 0
print(scores)