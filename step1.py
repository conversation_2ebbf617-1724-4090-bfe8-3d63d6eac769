import asyncio
import random
from typing import Tuple
from dataclasses import dataclass


@dataclass
class Statistics:
    total_picks: int = 0
    total_workers: int = 0
    average_picks: float = 0.0


@dataclass
class Worker:
    name: str = ""
    picks: int = 0

    # Now let's process the name and return an object based off a random number generator
    async def collect_apples(self) -> dict:
        print(f"Collecting data from {self.name}...")
        await asyncio.sleep(0.5)
        try:
            self.picks = random.randint(1, 100)
            result = {"source_name": self.name, "apples": self.picks}
            print(f"{self.name} collected {self.picks} apples")
            return result
        except ValueError as e:
            print(
                f"A name must be supplied to this wonderful and big beautfiul function"
            )
            return e


async def data_processor(apple_picker: Worker, statistics: dict) -> dict:
    try:
        source_name = apple_picker.name
        current_count = apple_picker.picks
        statistics.total_workers += 1
        statistics.total_picks += current_count
        statistics.average_picks = statistics.total_picks / statistics.total_workers

        print(
            f"{source_name} has picked {current_count} today. Calculating tomorrow"
            "s expected count..."
        )
        await asyncio.sleep(0.3)
        try:
            forecasted_apples = int(current_count) * 2
            apple_picker.picks = forecasted_apples
            print(
                f"{source_name} will pick {forecasted_apples} tomorrow if they are paid well enough"
            )
            return statistics
        except ValueError:
            print("Apple count must be an integer")
    except ValueError as e:
        print(
            f"A valid object must be supplied to this wonderful and big beautiful function: {e}"
        )


async def main():
    statistics = Statistics()
    # Create multiple apple pickers here
    jonathan = Worker("Jonathan")
    ben = Worker("Ben")
    brenna = Worker("Brenna")

    get_to_work = await asyncio.gather(
        jonathan.collect_apples(), ben.collect_apples(), brenna.collect_apples()
    )

    processed_results = await asyncio.gather(
        data_processor(jonathan, statistics),
        data_processor(brenna, statistics),
        data_processor(ben, statistics),
    )

    print(f"Average picks for the day: {statistics.average_picks}")


if __name__ == "__main__":
    asyncio.run(main())
