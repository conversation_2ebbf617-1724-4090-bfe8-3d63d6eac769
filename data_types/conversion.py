# Take a dictionary and convert it to a list
input_dict = {"dog_name1": "<PERSON><PERSON>", "dog_name2": "<PERSON><PERSON><PERSON>", "dog_name3": "<PERSON>", "dog_name4": "<PERSON>"}

output_list = []

for k, v in input_dict.items():
    output_list.append(v)

print(output_list)

output_list = []

for k, v in input_dict.items():
    output_list.append((k,v))

print(output_list)

output_list.sort(reverse=True)
new_dict = {}
for item in output_list:
    new_dict[item[0]] = item[1] if item[1] else None

print(new_dict)
